'use client';

import React from 'react';
import { ImageEditor } from '@/components/ImageEditor';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Top Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-800">Magic Eraser</h1>
            <div className="text-sm text-gray-500">
              Remove unwanted objects from your images with AI
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Header actions will go here */}
          </div>
        </div>
      </header>

      {/* Main Layout */}
      <div className="flex h-[calc(100vh-73px)]">
        {/* Left Side - Main Canvas Area */}
        <div className="flex-1 flex flex-col">
          <ImageEditor />
        </div>
      </div>
    </div>
  );
}